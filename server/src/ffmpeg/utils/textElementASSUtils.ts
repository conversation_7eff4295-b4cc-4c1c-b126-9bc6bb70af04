import { Caption, CaptionStyle } from "../types";
import { ASSSubtitleUtils } from "./assSubtitleUtils";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";

/**
 * 文字布局信息接口
 */
interface TextLayoutInfo {
  // 背景框信息
  backgroundX: number;
  backgroundY: number;
  backgroundWidth: number;
  backgroundHeight: number;

  // 文字位置信息（在背景框内的位置）
  textX: number;
  textY: number;
  textAlign: "left" | "center" | "right";
}

/**
 * ASS样式标签构建器接口
 */
interface ASSStyleTags {
  position: string;
  fontSize: string;
  fontStyle: string;
  color: string;
  stroke: string;
  charSpacing: string;
  shadow: string;
  opacity: string;
  lineHeight: string;
}

/**
 * 配置常量
 */
const CONFIG = {
  // 默认值
  DEFAULT_BACKGROUND_WIDTH: 200,
  DEFAULT_BACKGROUND_HEIGHT: 50,
  DEFAULT_FONT_SIZE: 24,
  DEFAULT_LINE_HEIGHT: 1.2,
  DEFAULT_TEXT_ALIGN: "center" as const,

  // 内边距
  PADDING_X: 10,
  PADDING_Y: 5,
  TEXT_MARGIN_X: 10,

  // 字符宽度比例
  CHINESE_CHAR_WIDTH_RATIO: 0.9,
  ENGLISH_CHAR_WIDTH_RATIO: 0.6,

  // 字体大小调整限制
  MIN_FONT_SIZE: 8,
  FONT_SIZE_MIN_RATIO: 0.3,
  FONT_SIZE_MAX_RATIO: 1.2,
  FONT_SIZE_ADJUSTMENT_THRESHOLD: 2,

  // 行高和间距
  LINE_SPACING_MULTIPLIER: 0.2,
  SHADOW_DISTANCE_RATIO: 0.5,
  SHADOW_BLUR_RATIO: 0.33,
  SHADOW_ALPHA: 160,

  // DPI适配
  DPI_SCALE_FACTOR: 1, // 默认2x DPI适配，匹配大多数现代设备
  ENABLE_DPI_SCALING: true, // 是否启用DPI适配

  // 圆角设置
  DEFAULT_BORDER_RADIUS: 10, // 默认圆角半径，与前端保持一致

  // 图层
  BACKGROUND_LAYER: 0,
  TEXT_LAYER: 1,

  // 对齐值映射
  ALIGNMENT_MAP: {
    left: 4,
    center: 5,
    right: 6,
  } as const,
} as const;

/**
 * 专门用于文字元素的ASS字幕工具类
 * 提供更精确的位置控制，专门为文字元素设计
 *
 * 主要功能：
 * - 创建文字元素的ASS字幕文件
 * - 支持背景色、渐变色、阴影、描边等样式
 * - 自动调整字体大小以适应背景框
 * - 支持多行文本和自定义行高
 * - 提供精确的位置控制
 *
 * <AUTHOR>
 * @version 2.0
 */
export class TextElementASSUtils {
  /**
   * 为文字元素创建ASS字幕文件
   * 使用绝对位置定位，确保与前端显示完全一致
   * 支持动画效果，确保与前端动画效果一致
   */
  static createTextElementASSFile(
    caption: Caption,
    style: CaptionStyle,
    canvasWidth: number,
    canvasHeight: number,
    elementOpacity?: number,
    animationInfo?: any
  ): string {
    this.validateInputs(caption, style, canvasWidth, canvasHeight);

    try {
      const assContent = this.generateTextElementASSContent(
        caption,
        style,
        canvasWidth,
        canvasHeight,
        elementOpacity,
        animationInfo
      );

      const subtitlePath = this.createTempFile(caption.id, assContent);
      console.log(`为文字元素 ${caption.id} 创建ASS字幕文件: ${subtitlePath}`);

      if (animationInfo) {
        console.log(`文字元素 ${caption.id} 包含动画效果:`, animationInfo);
      }

      return subtitlePath;
    } catch (error) {
      const errorMessage = `创建文字元素ASS文件失败: ${
        error instanceof Error ? error.message : String(error)
      }`;
      console.error(errorMessage, error);
      throw new Error(errorMessage);
    }
  }

  /**
   * 验证输入参数
   */
  private static validateInputs(
    caption: Caption,
    style: CaptionStyle,
    canvasWidth: number,
    canvasHeight: number
  ): void {
    if (!caption?.id) {
      throw new Error("Caption ID is required");
    }
    if (!caption.text?.trim()) {
      throw new Error("Caption text cannot be empty");
    }
    if (canvasWidth <= 0 || canvasHeight <= 0) {
      throw new Error("Canvas dimensions must be positive");
    }
    if (caption.startTime >= caption.endTime) {
      throw new Error("Invalid time range: start time must be before end time");
    }
  }

  /**
   * 创建临时文件
   */
  private static createTempFile(captionId: string, content: string): string {
    const tempDir = os.tmpdir();
    const timestamp = new Date()
      .toISOString()
      .replace(/[:.]/g, "-")
      .replace("T", "_")
      .replace("Z", "");

    const subtitlePath = path.join(
      tempDir,
      `text_element_${captionId}_${timestamp}.ass`
    );

    fs.writeFileSync(subtitlePath, content, "utf8");
    return subtitlePath;
  }

  /**
   * 生成文字元素的ASS文件内容
   * 支持动画效果
   */
  private static generateTextElementASSContent(
    caption: Caption,
    style: CaptionStyle,
    canvasWidth: number,
    canvasHeight: number,
    elementOpacity?: number,
    animationInfo?: any
  ): string {
    const parts: string[] = [];

    // 生成文件头部和样式
    parts.push(
      this.generateASSHeader(canvasWidth, canvasHeight),
      `Style: Default,${ASSSubtitleUtils.generateASSStyle(
        style,
        canvasWidth,
        canvasHeight
      )}\n`,
      `[Events]\nFormat: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n`
    );

    // 处理时间和布局
    const timeInfo = this.getTimeInfo(caption);
    const layoutInfo = this.calculateTextLayout(
      caption,
      style,
      canvasWidth,
      canvasHeight
    );

    // 添加背景和文字（包含动画效果）
    this.addDialogueLinesWithAnimation(
      parts,
      timeInfo,
      layoutInfo,
      caption,
      style,
      elementOpacity,
      animationInfo
    );

    return parts.join("");
  }

  /**
   * 获取时间信息
   */
  private static getTimeInfo(caption: Caption) {
    return {
      startTime: ASSSubtitleUtils.convertTimeToASS(caption.startTime),
      endTime: ASSSubtitleUtils.convertTimeToASS(caption.endTime),
    };
  }

  /**
   * 添加对话行
   */
  private static addDialogueLines(
    parts: string[],
    timeInfo: { startTime: string; endTime: string },
    layoutInfo: TextLayoutInfo,
    caption: Caption,
    style: CaptionStyle,
    elementOpacity?: number
  ): void {
    const { startTime, endTime } = timeInfo;

    // 背景层
    if (this.shouldRenderBackground(style)) {
      const backgroundRect = this.generateBackgroundRectangle(
        layoutInfo,
        style
      );
      parts.push(
        `Dialogue: ${CONFIG.BACKGROUND_LAYER},${startTime},${endTime},Default,,0,0,0,,${backgroundRect}\n`
      );
    }

    // 文字层
    const textContent = this.generateTextContent(
      caption,
      layoutInfo,
      style,
      elementOpacity
    );
    parts.push(
      `Dialogue: ${CONFIG.TEXT_LAYER},${startTime},${endTime},Default,,0,0,0,,${textContent}\n`
    );
  }

  /**
   * 添加带动画效果的对话行
   * 支持淡入淡出、滑动、擦除等动画效果
   */
  private static addDialogueLinesWithAnimation(
    parts: string[],
    timeInfo: { startTime: string; endTime: string },
    layoutInfo: TextLayoutInfo,
    caption: Caption,
    style: CaptionStyle,
    elementOpacity?: number,
    animationInfo?: any
  ): void {
    const { startTime, endTime } = timeInfo;

    // 如果没有动画信息，使用原来的方法
    if (!animationInfo) {
      this.addDialogueLines(
        parts,
        timeInfo,
        layoutInfo,
        caption,
        style,
        elementOpacity
      );
      return;
    }

    // 背景层（如果需要）
    if (this.shouldRenderBackground(style)) {
      const backgroundRect = this.generateBackgroundRectangle(
        layoutInfo,
        style
      );

      // 为背景添加动画效果，确保背景框跟随文字移动
      const animatedBackgroundRect = this.applyAnimationToBackground(
        backgroundRect,
        animationInfo,
        layoutInfo,
        timeInfo
      );

      parts.push(
        `Dialogue: ${CONFIG.BACKGROUND_LAYER},${startTime},${endTime},Default,,0,0,0,,${animatedBackgroundRect}\n`
      );
    }

    // 文字层（带动画效果）
    const textContent = this.generateTextContent(
      caption,
      layoutInfo,
      style,
      elementOpacity
    );

    // 为文字添加动画效果
    const animatedTextContent = this.applyAnimationToContent(
      textContent,
      animationInfo,
      layoutInfo,
      timeInfo
    );

    parts.push(
      `Dialogue: ${CONFIG.TEXT_LAYER},${startTime},${endTime},Default,,0,0,0,,${animatedTextContent}\n`
    );
  }

  /**
   * 判断是否应该渲染背景
   */
  private static shouldRenderBackground(style: CaptionStyle): boolean {
    return !!(style.backgroundColor && style.backgroundColor !== "transparent");
  }

  /**
   * 生成文字内容（不包含背景）
   */
  private static generateTextContent(
    caption: Caption,
    layoutInfo: TextLayoutInfo,
    style: CaptionStyle,
    elementOpacity?: number
  ): string {
    let escapedText = ASSSubtitleUtils.escapeASSText(caption.text);

    // 构建样式标签
    const styleTags = this.buildStyleTags(
      caption,
      layoutInfo,
      style,
      elementOpacity
    );

    // 处理多行文本的行高
    escapedText = this.processMultilineText(escapedText, style);

    // 组合所有标签
    return this.combineStyleTags(styleTags) + escapedText;
  }

  /**
   * 构建所有样式标签
   */
  private static buildStyleTags(
    caption: Caption,
    layoutInfo: TextLayoutInfo,
    style: CaptionStyle,
    elementOpacity?: number
  ): ASSStyleTags {
    const adjustedFontSize = this.calculateAdjustedFontSize(
      caption,
      layoutInfo,
      style
    );

    return {
      position: this.generateTextPositionTags(layoutInfo, style),
      fontSize:
        adjustedFontSize !== style.fontSize ? `{\\fs${adjustedFontSize}}` : "",
      fontStyle: this.generateFontStyleTags(style),
      color: this.generateColorTags(style),
      stroke: this.generateStrokeTags(style),
      charSpacing: this.generateCharSpacingTags(style),
      shadow: this.generateShadowTags(style),
      opacity: this.generateOpacityTags(style, elementOpacity),
      lineHeight: this.generateLineHeightTags(style),
    };
  }

  /**
   * 组合样式标签
   */
  private static combineStyleTags(tags: ASSStyleTags): string {
    return Object.values(tags).join("");
  }

  /**
   * 处理多行文本
   */
  private static processMultilineText(
    escapedText: string,
    style: CaptionStyle
  ): string {
    if (
      style.lineHeight &&
      style.lineHeight !== 1 &&
      escapedText.includes("\\N")
    ) {
      const lineSpacing = Math.round(
        (style.lineHeight - 1) *
          (style.fontSize || CONFIG.DEFAULT_FONT_SIZE) *
          CONFIG.LINE_SPACING_MULTIPLIER
      );

      if (lineSpacing > 0) {
        return escapedText.replace(/\\N/g, `\\N{\\fsp${lineSpacing}}`);
      }
    }
    return escapedText;
  }

  /**
   * 计算文字布局信息
   * 包括背景框位置和文字在框内的位置
   */
  private static calculateTextLayout(
    caption: Caption,
    style: CaptionStyle,
    canvasWidth: number,
    canvasHeight: number
  ): TextLayoutInfo {
    // 使用默认值或计算背景框尺寸
    const { backgroundWidth, backgroundHeight } =
      this.calculateBackgroundDimensions(caption, style);

    // 获取位置信息
    const backgroundX = style.positionX ?? 0;
    const backgroundY = style.positionY ?? 0;

    // 计算文字位置
    const { textX, textY } = this.calculateTextPosition(
      backgroundX,
      backgroundY,
      backgroundWidth,
      backgroundHeight,
      style.textAlign || CONFIG.DEFAULT_TEXT_ALIGN
    );

    return {
      backgroundX,
      backgroundY,
      backgroundWidth,
      backgroundHeight,
      textX,
      textY,
      textAlign: style.textAlign || CONFIG.DEFAULT_TEXT_ALIGN,
    };
  }

  /**
   * 计算背景框尺寸
   */
  private static calculateBackgroundDimensions(
    caption: Caption,
    style: CaptionStyle
  ): { backgroundWidth: number; backgroundHeight: number } {
    // 如果没有位置信息，使用默认值
    if (style.positionX === undefined || style.positionY === undefined) {
      return {
        backgroundWidth: CONFIG.DEFAULT_BACKGROUND_WIDTH,
        backgroundHeight: CONFIG.DEFAULT_BACKGROUND_HEIGHT,
      };
    }

    if (style.width && style.height) {
      console.log(`使用前端提供的文字框尺寸: ${style.width}x${style.height}`);
      return { backgroundWidth: style.width, backgroundHeight: style.height };
    }

    // 估算文字尺寸
    const fontSize = style.fontSize || CONFIG.DEFAULT_FONT_SIZE;
    const textLength = caption.text?.length || 10;
    const lineHeight = style.lineHeight || CONFIG.DEFAULT_LINE_HEIGHT;

    const charWidthRatio = this.isChineseText(caption.text)
      ? CONFIG.CHINESE_CHAR_WIDTH_RATIO
      : CONFIG.ENGLISH_CHAR_WIDTH_RATIO;

    const estimatedTextWidth = textLength * fontSize * charWidthRatio;
    const estimatedTextHeight = fontSize * lineHeight;

    const backgroundWidth = estimatedTextWidth + CONFIG.PADDING_X * 2;
    const backgroundHeight = estimatedTextHeight + CONFIG.PADDING_Y * 2;

    console.log(`估算的文字框尺寸: ${backgroundWidth}x${backgroundHeight}`);
    return { backgroundWidth, backgroundHeight };
  }

  /**
   * 计算文字位置
   */
  private static calculateTextPosition(
    backgroundX: number,
    backgroundY: number,
    backgroundWidth: number,
    backgroundHeight: number,
    textAlign: "left" | "center" | "right"
  ): { textX: number; textY: number } {
    // 垂直居中
    const textY = backgroundY + backgroundHeight / 2;

    // 水平位置根据对齐方式确定
    let textX: number;
    switch (textAlign) {
      case "left":
        textX = backgroundX + CONFIG.TEXT_MARGIN_X;
        break;
      case "right":
        textX = backgroundX + backgroundWidth - CONFIG.TEXT_MARGIN_X;
        break;
      default: // center
        textX = backgroundX + backgroundWidth / 2;
        break;
    }

    return { textX, textY };
  }

  /**
   * 生成文字位置标签
   * 根据布局信息在背景框内正确定位文字
   */
  private static generateTextPositionTags(
    layoutInfo: TextLayoutInfo,
    style: CaptionStyle
  ): string {
    const xPos = Math.round(layoutInfo.textX);
    const yPos = Math.round(layoutInfo.textY);

    // 根据textAlign确定对齐方式
    let alignmentValue = CONFIG.ALIGNMENT_MAP[layoutInfo.textAlign] || 4; // 默认为左中对齐

    return `{\\an${alignmentValue}\\pos(${xPos},${yPos})}`;
  }

  /**
   * 生成颜色标签（支持渐变和普通颜色）
   */
  private static generateColorTags(style: CaptionStyle): string {
    if (
      style.useGradient &&
      style.gradientColors &&
      style.gradientColors.length >= 2
    ) {
      // 渐变色
      const color1 = ASSSubtitleUtils.convertColorToASS(
        style.gradientColors[0]
      );
      const color2 = ASSSubtitleUtils.convertColorToASS(
        style.gradientColors[1]
      );
      return `{\\1c${color1}\\3c${color2}}`;
    } else {
      // 普通颜色
      const fontColor = ASSSubtitleUtils.convertColorToASS(style.fontColor);
      return `{\\1c${fontColor}}`;
    }
  }

  /**
   * 生成渐变标签（保留向后兼容）
   */
  private static generateGradientTags(style: CaptionStyle): string {
    return this.generateColorTags(style);
  }

  /**
   * 生成字体样式标签（粗体、斜体等）
   * 注意：下划线和删除线已在ASS样式行中处理，不需要在Dialogue行中重复
   */
  private static generateFontStyleTags(style: CaptionStyle): string {
    let styleTags = "";

    if (style.styles && style.styles.length > 0) {
      // 粗体 - 可以在Dialogue行中覆盖样式行设置
      if (style.styles.includes("bold") || style.fontWeight >= 700) {
        styleTags += "\\b1";
      }

      // 斜体 - 可以在Dialogue行中覆盖样式行设置
      if (style.styles.includes("italic")) {
        styleTags += "\\i1";
      }

      // 下划线和删除线已在ASS样式行中通过Underline和StrikeOut参数处理
      // 使用样式行的方式可以获得更好的渲染效果，特别是连续下划线(-1)
      // 因此这里不再添加 \u1 和 \s1 标签，避免重复

      console.log(`字体样式标签生成 - 下划线和删除线将通过ASS样式行处理`);
    }

    return styleTags ? `{${styleTags}}` : "";
  }

  /**
   * 生成字符间距标签
   */
  private static generateCharSpacingTags(style: CaptionStyle): string {
    if (!style.charSpacing || style.charSpacing === 0) {
      return "";
    }

    // 直接使用字符间距值，不进行缩放
    const assCharSpacing = Math.round(style.charSpacing);
    return `{\\fsp${assCharSpacing}}`;
  }

  /**
   * 生成阴影标签
   */
  private static generateShadowTags(style: CaptionStyle): string {
    let shadowTags = "";

    // 检查是否有自定义阴影偏移
    const hasCustomOffset =
      style.shadowOffsetX !== 0 || style.shadowOffsetY !== 0;

    // 如果有自定义偏移，使用 \xshad 和 \yshad；否则使用 \shad
    if (hasCustomOffset) {
      let adjustedOffsetX = style.shadowOffsetX || 0;
      let adjustedOffsetY = style.shadowOffsetY || 0;

      // 考虑缩放因子
      const scaleX = style.scaleX || 1;
      const scaleY = style.scaleY || 1;
      adjustedOffsetX = Math.round(adjustedOffsetX * scaleX);
      adjustedOffsetY = Math.round(adjustedOffsetY * scaleY);

      // DPI适配：为了匹配前端在高DPI显示器上的视觉效果
      if (CONFIG.ENABLE_DPI_SCALING) {
        adjustedOffsetX = Math.round(adjustedOffsetX * CONFIG.DPI_SCALE_FACTOR);
        adjustedOffsetY = Math.round(adjustedOffsetY * CONFIG.DPI_SCALE_FACTOR);

        console.log(
          `阴影偏移缩放和DPI适配: 原始(${style.shadowOffsetX}, ${
            style.shadowOffsetY
          }) -> 缩放后(${(style.shadowOffsetX || 0) * scaleX}, ${
            (style.shadowOffsetY || 0) * scaleY
          }) -> 最终(${adjustedOffsetX}, ${adjustedOffsetY}) [缩放:${scaleX}x${scaleY}, DPI:${
            CONFIG.DPI_SCALE_FACTOR
          }x]`
        );
      } else {
        console.log(
          `阴影偏移缩放: 原始(${style.shadowOffsetX}, ${style.shadowOffsetY}) -> 缩放后(${adjustedOffsetX}, ${adjustedOffsetY}) [${scaleX}x${scaleY}]`
        );
      }

      shadowTags += `\\xshad${adjustedOffsetX}`;
      shadowTags += `\\yshad${adjustedOffsetY}`;
    } else if (style.shadowBlur > 0) {
      // 没有自定义偏移时，根据shadowBlur计算阴影距离，也要考虑缩放
      const scaleX = style.scaleX || 1;
      const scaleY = style.scaleY || 1;
      const scale = Math.max(scaleX, scaleY);
      const shadowDistance = Math.max(
        1,
        Math.round(style.shadowBlur * CONFIG.SHADOW_DISTANCE_RATIO * scale)
      );
      shadowTags += `\\shad${shadowDistance}`;
    }

    // 阴影颜色和透明度
    if (style.shadowColor && style.shadowColor !== "transparent") {
      const shadowColor = ASSSubtitleUtils.convertColorToASS(style.shadowColor);
      shadowTags += `\\4c${shadowColor}`;

      // 根据shadowBlur调整阴影透明度来模拟模糊效果
      // shadowBlur越大，阴影越透明，视觉上更像模糊效果
      const baseAlpha = CONFIG.SHADOW_ALPHA;
      const blurAdjustment = Math.min(style.shadowBlur * 15, 100); // 限制最大调整量
      const adjustedAlpha = Math.min(baseAlpha + blurAdjustment, 200);

      const alphaValue = (255 - adjustedAlpha).toString(16).padStart(2, "0");
      shadowTags += `\\4a&H${alphaValue}&`;
    }

    return shadowTags ? `{${shadowTags}}` : "";
  }

  /**
   * 生成描边标签
   */
  private static generateStrokeTags(style: CaptionStyle): string {
    if (!style.strokeWidth || style.strokeWidth <= 0) {
      return "";
    }

    // 考虑缩放因子
    const scaleX = style.scaleX || 1;
    const scaleY = style.scaleY || 1;
    const scale = Math.max(scaleX, scaleY); // 使用较大的缩放值来保持描边比例

    // ASS描边渲染比fabric.js细，需要放大约1.5倍来匹配视觉效果，同时考虑缩放
    const adjustedStrokeWidth = Math.round(style.strokeWidth * scale);
    const strokeColor = ASSSubtitleUtils.convertColorToASS(style.strokeColor);

    console.log(
      `描边宽度缩放: 原始(${style.strokeWidth}) -> 缩放后(${adjustedStrokeWidth}) [${scale}x]`
    );

    return `{\\3c${strokeColor}\\bord${adjustedStrokeWidth}}`;
  }

  /**
   * 生成透明度标签
   */
  private static generateOpacityTags(
    style: CaptionStyle,
    elementOpacity?: number
  ): string {
    const finalOpacity = this.calculateFinalOpacity(style, elementOpacity);

    if (finalOpacity >= 1) {
      return ""; // 完全不透明，不需要透明度标签
    }

    // ASS透明度值：0 = 完全不透明，255 = 完全透明
    const alphaValue = Math.round((1 - finalOpacity) * 255);
    const alphaHex = alphaValue.toString(16).padStart(2, "0").toUpperCase();

    return `{\\1a&H${alphaHex}&}`;
  }

  /**
   * 计算最终透明度
   */
  private static calculateFinalOpacity(
    style: CaptionStyle,
    elementOpacity?: number
  ): number {
    const styleOpacity = style.opacity ?? 1;
    return elementOpacity !== undefined
      ? styleOpacity * elementOpacity
      : styleOpacity;
  }

  /**
   * 生成行高标签
   */
  private static generateLineHeightTags(style: CaptionStyle): string {
    if (!style.lineHeight || style.lineHeight === 1) {
      return ""; // 默认行高，不需要标签
    }

    // 对于多行文本，我们需要在换行符处添加适当的间距
    // ASS中没有直接的行高控制，我们通过调整字符间距来模拟
    const lineSpacing = Math.round(
      (style.lineHeight - 1) *
        (style.fontSize || CONFIG.DEFAULT_FONT_SIZE) *
        CONFIG.LINE_SPACING_MULTIPLIER
    );

    if (lineSpacing > 0) {
      return `{\\fsp${lineSpacing}}`;
    }

    return "";
  }

  /**
   * 生成圆角矩形路径
   * 使用ASS的绘图功能(\p标签)绘制圆角矩形背景
   */
  private static generateRoundedRectanglePath(
    width: number,
    height: number,
    radius: number = CONFIG.DEFAULT_BORDER_RADIUS
  ): string {
    // 确保圆角半径不超过矩形的一半
    const r = Math.min(radius, Math.min(width, height) / 2);

    if (r <= 0) {
      // 如果圆角半径为0或负数，绘制普通矩形
      return `m 0 0 l ${width} 0 l ${width} ${height} l 0 ${height}`;
    }

    // 绘制圆角矩形路径
    // 从左上角圆角开始，顺时针绘制
    // 使用贝塞尔曲线近似圆角，控制点距离为半径的约0.552倍（圆的贝塞尔近似常数）
    const controlOffset = r * 0.552;

    const path = [
      `m ${r} 0`, // 移动到左上角圆角结束位置
      `l ${width - r} 0`, // 上边线
      `b ${width - r + controlOffset} 0 ${width} ${
        r - controlOffset
      } ${width} ${r}`, // 右上角圆角
      `l ${width} ${height - r}`, // 右边线
      `b ${width} ${height - r + controlOffset} ${
        width - r + controlOffset
      } ${height} ${width - r} ${height}`, // 右下角圆角
      `l ${r} ${height}`, // 下边线
      `b ${r - controlOffset} ${height} 0 ${height - r + controlOffset} 0 ${
        height - r
      }`, // 左下角圆角
      `l 0 ${r}`, // 左边线
      `b 0 ${r - controlOffset} ${r - controlOffset} 0 ${r} 0`, // 左上角圆角
    ].join(" ");

    return path;
  }

  /**
   * 生成背景矩形
   * 使用ASS的绘图功能(\p标签)绘制圆角矩形背景
   * 使用相对坐标以支持move动画
   */
  private static generateBackgroundRectangle(
    layoutInfo: TextLayoutInfo,
    style: CaptionStyle
  ): string {
    const bgColor = ASSSubtitleUtils.convertColorToASS(style.backgroundColor);

    // 使用相对坐标，以背景框左上角为原点
    const width = layoutInfo.backgroundWidth;
    const height = layoutInfo.backgroundHeight;

    // 生成圆角矩形路径
    const roundedRectPath = this.generateRoundedRectanglePath(
      width,
      height,
      CONFIG.DEFAULT_BORDER_RADIUS
    );

    // 使用an7对齐方式（左上角对齐）和pos定位到背景框位置
    // 这样move标签就能正常工作
    return `{\\an7\\pos(${layoutInfo.backgroundX},${layoutInfo.backgroundY})\\1c${bgColor}\\3c${bgColor}\\p1}${roundedRectPath}{\\p0}`;
  }

  /**
   * 计算矩形坐标
   */
  private static calculateRectangleCoordinates(layoutInfo: TextLayoutInfo) {
    const x1 = Math.round(layoutInfo.backgroundX);
    const y1 = Math.round(layoutInfo.backgroundY);
    const x2 = Math.round(layoutInfo.backgroundX + layoutInfo.backgroundWidth);
    const y2 = Math.round(layoutInfo.backgroundY + layoutInfo.backgroundHeight);

    return { x1, y1, x2, y2 };
  }

  /**
   * 生成ASS文件头部
   */
  private static generateASSHeader(
    canvasWidth: number,
    canvasHeight: number
  ): string {
    return `[Script Info]
Title: Text Element Subtitles
ScriptType: v4.00+
WrapStyle: 0
ScaledBorderAndShadow: yes
YCbCr Matrix: None
PlayResX: ${canvasWidth}
PlayResY: ${canvasHeight}

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
`;
  }

  /**
   * 计算调整后的字体大小，确保文字适合背景框
   */
  private static calculateAdjustedFontSize(
    caption: Caption,
    layoutInfo: TextLayoutInfo,
    style: CaptionStyle
  ): number {
    const originalFontSize = style.fontSize || CONFIG.DEFAULT_FONT_SIZE;

    // 如果没有背景框，直接返回原始字体大小
    if (!this.shouldRenderBackground(style)) {
      return originalFontSize;
    }

    const availableSpace = this.calculateAvailableSpace(layoutInfo);
    const maxFontSize = this.calculateMaxFontSize(
      caption,
      style,
      availableSpace
    );
    const adjustedFontSize = this.constrainFontSize(
      maxFontSize,
      originalFontSize
    );

    // 如果调整幅度很小，就不调整
    if (
      Math.abs(adjustedFontSize - originalFontSize) <
      CONFIG.FONT_SIZE_ADJUSTMENT_THRESHOLD
    ) {
      return originalFontSize;
    }

    console.log(
      `字体大小调整: ${originalFontSize}px → ${Math.round(
        adjustedFontSize
      )}px (背景框: ${layoutInfo.backgroundWidth}x${
        layoutInfo.backgroundHeight
      })`
    );

    return Math.round(adjustedFontSize);
  }

  /**
   * 计算可用空间
   */
  private static calculateAvailableSpace(layoutInfo: TextLayoutInfo) {
    return {
      width: layoutInfo.backgroundWidth - CONFIG.PADDING_X * 2,
      height: layoutInfo.backgroundHeight - CONFIG.PADDING_Y * 2,
    };
  }

  /**
   * 计算最大字体大小
   */
  private static calculateMaxFontSize(
    caption: Caption,
    style: CaptionStyle,
    availableSpace: { width: number; height: number }
  ): number {
    const textLength = caption.text?.length || 1;
    const isChineseText = this.isChineseText(caption.text);

    // 根据可用高度计算最大字体大小
    const maxFontSizeByHeight =
      availableSpace.height / (style.lineHeight || CONFIG.DEFAULT_LINE_HEIGHT);

    // 根据可用宽度计算最大字体大小
    const charWidthRatio = isChineseText
      ? CONFIG.CHINESE_CHAR_WIDTH_RATIO
      : CONFIG.ENGLISH_CHAR_WIDTH_RATIO;
    const maxFontSizeByWidth =
      availableSpace.width / (textLength * charWidthRatio);

    return Math.min(maxFontSizeByHeight, maxFontSizeByWidth);
  }

  /**
   * 限制字体大小范围
   */
  private static constrainFontSize(
    maxFontSize: number,
    originalFontSize: number
  ): number {
    const minFontSize = Math.max(
      CONFIG.MIN_FONT_SIZE,
      originalFontSize * CONFIG.FONT_SIZE_MIN_RATIO
    );
    const maxAllowedSize = originalFontSize * CONFIG.FONT_SIZE_MAX_RATIO;

    return Math.max(minFontSize, Math.min(maxFontSize, maxAllowedSize));
  }

  /**
   * 检测文本是否包含中文字符
   */
  private static isChineseText(text: string): boolean {
    if (!text) return false;
    // 检测中文字符的正则表达式
    const chineseRegex = /[\u4e00-\u9fff]/;
    return chineseRegex.test(text);
  }

  /**
   * 为内容应用动画效果
   * 生成ASS动画标签，完全对应旧的drawtext方式的xfade滤镜效果
   */
  private static applyAnimationToContent(
    content: string,
    animationInfo: any,
    layoutInfo: TextLayoutInfo,
    timeInfo: { startTime: string; endTime: string }
  ): string {
    let animatedContent = content;
    const animationTags: string[] = [];

    // 特殊处理fade动画：如果同时有入场和出场fade动画，需要合并为一个fad标签
    const hasFadeIn = animationInfo.fadeIn?.type === "fade";
    const hasFadeOut = animationInfo.fadeOut?.type === "fade";

    if (hasFadeIn && hasFadeOut) {
      // 同时有入场和出场fade动画，合并为一个fad标签
      const fadeInDuration = animationInfo.fadeIn.duration || 1000;
      const fadeOutDuration = animationInfo.fadeOut.duration || 1000;
      const combinedFadeTag = `\\fad(${fadeInDuration},${fadeOutDuration})`;
      animationTags.push(combinedFadeTag);
    } else {
      // 处理入场动画
      if (animationInfo.fadeIn) {
        const animationTag = this.generateAnimationTag(
          animationInfo.fadeIn,
          layoutInfo,
          timeInfo,
          "in"
        );
        if (animationTag) {
          animationTags.push(animationTag);
        }
      }

      // 处理出场动画
      if (animationInfo.fadeOut) {
        const animationTag = this.generateAnimationTag(
          animationInfo.fadeOut,
          layoutInfo,
          timeInfo,
          "out"
        );
        if (animationTag) {
          animationTags.push(animationTag);
        }
      }
    }

    // 将动画标签添加到内容前面
    if (animationTags.length > 0) {
      // 检查是否包含移动动画，如果有则移除pos标签避免冲突
      const hasMove = animationTags.some((tag) => tag.includes("\\move("));
      if (hasMove) {
        // 提取对齐标签，保留对齐方式
        const alignMatch = content.match(/\\an(\d+)/);
        const alignTag = alignMatch ? `\\an${alignMatch[1]}` : "";

        // 移除原有的pos标签，但保留对齐标签
        const contentWithoutPos = content.replace(
          /\{\\an\d+\\pos\([^)]+\)\}/,
          ""
        );

        // 将对齐标签添加到动画标签中
        const finalAnimationTags = alignTag
          ? `${alignTag}${animationTags.join("")}`
          : animationTags.join("");
        animatedContent = `{${finalAnimationTags}}${contentWithoutPos}`;
      } else {
        animatedContent = `{${animationTags.join("")}}${content}`;
      }
    }

    return animatedContent;
  }

  /**
   * 为背景框应用动画效果
   * 确保背景框在动画过程中始终跟随文字移动
   */
  private static applyAnimationToBackground(
    backgroundRect: string,
    animationInfo: any,
    layoutInfo: TextLayoutInfo,
    timeInfo: { startTime: string; endTime: string }
  ): string {
    let animatedBackground = backgroundRect;
    const animationTags: string[] = [];

    // 特殊处理fade动画：如果同时有入场和出场fade动画，需要合并为一个fad标签
    const hasFadeIn = animationInfo.fadeIn?.type === "fade";
    const hasFadeOut = animationInfo.fadeOut?.type === "fade";

    if (hasFadeIn && hasFadeOut) {
      // 同时有入场和出场fade动画，合并为一个fad标签
      const fadeInDuration = animationInfo.fadeIn.duration || 1000;
      const fadeOutDuration = animationInfo.fadeOut.duration || 1000;
      const combinedFadeTag = `\\fad(${fadeInDuration},${fadeOutDuration})`;
      animationTags.push(combinedFadeTag);
    } else {
      // 处理入场动画
      if (animationInfo.fadeIn) {
        const animationTag = this.generateBackgroundAnimationTag(
          animationInfo.fadeIn,
          layoutInfo,
          timeInfo,
          "in"
        );
        if (animationTag) {
          animationTags.push(animationTag);
        }
      }

      // 处理出场动画
      if (animationInfo.fadeOut) {
        const animationTag = this.generateBackgroundAnimationTag(
          animationInfo.fadeOut,
          layoutInfo,
          timeInfo,
          "out"
        );
        if (animationTag) {
          animationTags.push(animationTag);
        }
      }
    }

    // 将动画标签添加到背景内容前面
    if (animationTags.length > 0) {
      // 提取背景矩形的位置信息并替换为动画位置
      animatedBackground = this.replaceBackgroundPosition(
        backgroundRect,
        animationTags.join("")
      );
    }

    return animatedBackground;
  }

  /**
   * 替换背景矩形的位置信息
   * 将pos标签替换为move标签以支持动画
   */
  private static replaceBackgroundPosition(
    backgroundRect: string,
    animationTags: string
  ): string {
    // 检查是否包含move动画标签
    if (animationTags.includes("\\move(")) {
      // 移除原有的pos标签，因为move标签会覆盖pos标签
      const withoutPos = backgroundRect.replace(/\\pos\([^)]+\)/, "");

      // 在样式标签中插入动画标签
      const match = withoutPos.match(/^(\{[^}]*\})(.*)/);
      if (match) {
        const [, styleTags, content] = match;
        // 在样式标签末尾插入动画标签
        const newStyleTags = styleTags.slice(0, -1) + animationTags + "}";
        return newStyleTags + content;
      }
    } else {
      // 对于非移动动画（如fade），保持原有的pos标签
      const match = backgroundRect.match(/^(\{[^}]*\})(.*)/);
      if (match) {
        const [, styleTags, content] = match;
        // 在样式标签末尾插入动画标签
        const newStyleTags = styleTags.slice(0, -1) + animationTags + "}";
        return newStyleTags + content;
      }
    }

    // 如果没有匹配到，直接在前面添加动画标签
    return `{${animationTags}}${backgroundRect}`;
  }

  /**
   * 生成背景框专用的动画标签
   * 计算背景框的移动轨迹，确保始终框住文字
   */
  private static generateBackgroundAnimationTag(
    animation: any,
    layoutInfo: TextLayoutInfo,
    timeInfo: { startTime: string; endTime: string },
    direction: "in" | "out"
  ): string {
    const animationType = animation.type;

    switch (animationType) {
      case "fade":
        return this.generateFadeAnimation(animation, timeInfo, direction);

      case "slide":
        return this.generateBackgroundMoveAnimation(
          animation,
          layoutInfo,
          timeInfo,
          direction
        );

      case "wipe":
        // 背景框的wipe动画也使用clip效果，与文字保持一致
        const duration = animation.duration || 1000;
        const wipeDirection = animation.wipeDirection || "left";

        const startTime = this.parseTimeToMs(timeInfo.startTime);
        const endTime =
          direction === "in"
            ? startTime + duration
            : this.parseTimeToMs(timeInfo.endTime);
        const animStartTime =
          direction === "in" ? startTime : endTime - duration;
        const animEndTime = direction === "in" ? startTime + duration : endTime;

        return this.generateWipeClipAnimation(
          layoutInfo,
          wipeDirection,
          direction,
          animStartTime,
          animEndTime
        );

      case "zoom":
        return this.generateZoomAnimation(
          animation,
          layoutInfo,
          timeInfo,
          direction
        );

      default:
        return this.generateFadeAnimation(
          { ...animation, type: "fade" },
          timeInfo,
          direction
        );
    }
  }

  /**
   * 生成背景框的移动动画
   * 计算背景框的起始和结束位置，确保跟随文字移动
   */
  private static generateBackgroundMoveAnimation(
    animation: any,
    layoutInfo: TextLayoutInfo,
    timeInfo: { startTime: string; endTime: string },
    direction: "in" | "out"
  ): string {
    const duration = animation.duration || 1000;
    const moveDirection =
      animation.slideDirection || animation.wipeDirection || "left";

    // 计算背景框的移动位置
    const { startX, startY, endX, endY } =
      this.calculateBackgroundMovePositions(
        layoutInfo,
        moveDirection,
        direction
      );

    const startTime = this.parseTimeToMs(timeInfo.startTime);
    const endTime =
      direction === "in"
        ? startTime + duration
        : this.parseTimeToMs(timeInfo.endTime);
    const moveStartTime = direction === "in" ? startTime : endTime - duration;
    const moveEndTime = direction === "in" ? startTime + duration : endTime;

    // 使用ASS的move标签实现背景框移动
    return `\\move(${startX},${startY},${endX},${endY},${moveStartTime},${moveEndTime})`;
  }

  /**
   * 计算背景框的移动位置
   * 确保背景框始终跟随文字移动，从视频外部开始
   */
  private static calculateBackgroundMovePositions(
    layoutInfo: TextLayoutInfo,
    moveDirection: string,
    direction: "in" | "out"
  ): { startX: number; startY: number; endX: number; endY: number } {
    // 背景框的正常位置
    const normalX = layoutInfo.backgroundX;
    const normalY = layoutInfo.backgroundY;

    // 获取画布尺寸
    const canvasWidth = 1920;
    const canvasHeight = 1080;

    let startX = normalX;
    let startY = normalY;
    let endX = normalX;
    let endY = normalY;

    if (direction === "in") {
      // 入场动画：背景框从视频外部移动到正常位置
      endX = normalX;
      endY = normalY;

      switch (moveDirection) {
        case "left":
          // 从左侧视频外部开始
          startX = -200 - layoutInfo.backgroundWidth; // 确保背景框完全在外部
          break;
        case "right":
          // 从右侧视频外部开始
          startX = canvasWidth + 200;
          break;
        case "up":
          // 从上方视频外部开始
          startY = -100 - layoutInfo.backgroundHeight;
          break;
        case "down":
          // 从下方视频外部开始
          startY = canvasHeight + 100;
          break;
      }
    } else {
      // 出场动画：背景框从正常位置移动到视频外部
      startX = normalX;
      startY = normalY;

      switch (moveDirection) {
        case "left":
          // 移动到左侧视频外部
          endX = -200 - layoutInfo.backgroundWidth;
          break;
        case "right":
          // 移动到右侧视频外部
          endX = canvasWidth + 200;
          break;
        case "up":
          // 移动到上方视频外部
          endY = -100 - layoutInfo.backgroundHeight;
          break;
        case "down":
          // 移动到下方视频外部
          endY = canvasHeight + 100;
          break;
      }
    }

    return { startX, startY, endX, endY };
  }

  /**
   * 生成统一的动画标签
   * 支持fade、slide、wipe、zoom等所有动画类型
   */
  private static generateAnimationTag(
    animation: any,
    layoutInfo: TextLayoutInfo,
    timeInfo: { startTime: string; endTime: string },
    direction: "in" | "out"
  ): string {
    const animationType = animation.type;
    const duration = animation.duration || 1000;

    switch (animationType) {
      case "fade":
        return this.generateFadeAnimation(animation, timeInfo, direction);

      case "slide":
        return this.generateSlideAnimation(
          animation,
          layoutInfo,
          timeInfo,
          direction
        );

      case "wipe":
        return this.generateWipeAnimation(
          animation,
          layoutInfo,
          timeInfo,
          direction
        );

      case "zoom":
        return this.generateZoomAnimation(
          animation,
          layoutInfo,
          timeInfo,
          direction
        );

      default:
        console.log(`不支持的动画类型: ${animationType}，使用fade作为默认`);
        return this.generateFadeAnimation(
          { ...animation, type: "fade" },
          timeInfo,
          direction
        );
    }
  }

  /**
   * 生成淡入淡出动画标签
   * 统一处理fade动画的入场和出场效果
   */
  private static generateFadeAnimation(
    animation: any,
    timeInfo: { startTime: string; endTime: string },
    direction: "in" | "out"
  ): string {
    const duration = animation.duration || 1000; // 默认1秒

    if (direction === "in") {
      // 淡入动画：从完全透明到不透明
      return `\\fad(${duration},0)`;
    } else {
      // 淡出动画：从不透明到完全透明
      return `\\fad(0,${duration})`;
    }
  }

  /**
   * 生成滑动动画标签
   */
  private static generateSlideAnimation(
    animation: any,
    layoutInfo: TextLayoutInfo,
    timeInfo: { startTime: string; endTime: string },
    direction: "in" | "out"
  ): string {
    if (animation.type !== "slide") {
      return "";
    }

    const duration = animation.duration || 1000;
    const slideDirection = animation.slideDirection || "left";

    // 计算滑动的起始和结束位置
    const { startX, startY, endX, endY } = this.calculateSlidePositions(
      layoutInfo,
      slideDirection,
      direction
    );

    // 使用ASS的move标签实现滑动效果
    const startTime = this.parseTimeToMs(timeInfo.startTime);
    const endTime =
      direction === "in"
        ? startTime + duration
        : this.parseTimeToMs(timeInfo.endTime);
    const moveStartTime = direction === "in" ? startTime : endTime - duration;
    const moveEndTime = direction === "in" ? startTime + duration : endTime;

    return `\\move(${startX},${startY},${endX},${endY},${moveStartTime},${moveEndTime})`;
  }

  /**
   * 计算滑动位置
   * 确保slide动画从视频外部开始切入
   */
  private static calculateSlidePositions(
    layoutInfo: TextLayoutInfo,
    slideDirection: string,
    direction: "in" | "out"
  ): { startX: number; startY: number; endX: number; endY: number } {
    const normalX = layoutInfo.textX;
    const normalY = layoutInfo.textY;

    // 获取画布尺寸（假设从全局或传递进来，这里使用常见的1920x1080）
    const canvasWidth = 1920;
    const canvasHeight = 1080;

    let startX = normalX;
    let startY = normalY;
    let endX = normalX;
    let endY = normalY;

    if (direction === "in") {
      // 入场动画：从视频外部滑动到正常位置
      endX = normalX;
      endY = normalY;

      switch (slideDirection) {
        case "left":
          // 从左侧视频外部开始（负坐标）
          startX = -200; // 确保完全在视频外部
          break;
        case "right":
          // 从右侧视频外部开始
          startX = canvasWidth + 200;
          break;
        case "up":
          // 从上方视频外部开始
          startY = -100;
          break;
        case "down":
          // 从下方视频外部开始
          startY = canvasHeight + 100;
          break;
      }
    } else {
      // 出场动画：从正常位置滑动到视频外部
      startX = normalX;
      startY = normalY;

      switch (slideDirection) {
        case "left":
          // 滑动到左侧视频外部
          endX = -200;
          break;
        case "right":
          // 滑动到右侧视频外部
          endX = canvasWidth + 200;
          break;
        case "up":
          // 滑动到上方视频外部
          endY = -100;
          break;
        case "down":
          // 滑动到下方视频外部
          endY = canvasHeight + 100;
          break;
      }
    }

    return { startX, startY, endX, endY };
  }

  /**
   * 生成擦除动画标签
   * 使用clip标签实现真正的擦除效果，对应xfade的wipe系列
   */
  private static generateWipeAnimation(
    animation: any,
    layoutInfo: TextLayoutInfo,
    timeInfo: { startTime: string; endTime: string },
    direction: "in" | "out"
  ): string {
    const duration = animation.duration || 1000;
    const wipeDirection = animation.wipeDirection || "left";

    // 计算动画时间
    const startTime = this.parseTimeToMs(timeInfo.startTime);
    const endTime =
      direction === "in"
        ? startTime + duration
        : this.parseTimeToMs(timeInfo.endTime);
    const animStartTime = direction === "in" ? startTime : endTime - duration;
    const animEndTime = direction === "in" ? startTime + duration : endTime;

    // 生成动态clip标签实现擦除效果
    const clipAnimation = this.generateWipeClipAnimation(
      layoutInfo,
      wipeDirection,
      direction,
      animStartTime,
      animEndTime
    );

    return clipAnimation;
  }

  /**
   * 生成擦除动画的clip标签
   * 使用ASS的\clip()标签实现真正的擦除效果
   */
  private static generateWipeClipAnimation(
    layoutInfo: TextLayoutInfo,
    wipeDirection: string,
    direction: "in" | "out",
    startTime: number,
    endTime: number
  ): string {
    // 计算文字区域边界（使用背景框位置和尺寸）
    const bgX = Math.round(layoutInfo.backgroundX);
    const bgY = Math.round(layoutInfo.backgroundY);
    const bgWidth = Math.round(layoutInfo.backgroundWidth + 10);
    const bgHeight = Math.round(layoutInfo.backgroundHeight + 10);

    // 计算完整的显示区域
    const left = bgX;
    const right = bgX + bgWidth;
    const top = bgY;
    const bottom = bgY + bgHeight;

    // 根据擦除方向和动画方向生成clip标签
    let clipTag: string;

    switch (wipeDirection) {
      case "left":
        if (direction === "in") {
          // 左擦除入场：从左边开始逐渐显示
          // 起始：只显示最左边的一条线
          // 结束：显示完整区域
          clipTag = `\\clip(${left},${top},${
            left + 1
          },${bottom})\\t(${startTime},${endTime},\\clip(${left},${top},${right},${bottom}))`;
        } else {
          // 左擦除出场：从右边开始逐渐隐藏
          // 起始：显示完整区域
          // 结束：只显示最左边的一条线
          clipTag = `\\clip(${left},${top},${right},${bottom})\\t(${startTime},${endTime},\\clip(${left},${top},${
            left + 1
          },${bottom}))`;
        }
        break;

      case "right":
        if (direction === "in") {
          // 右擦除入场：从右边开始逐渐显示
          clipTag = `\\clip(${
            right - 1
          },${top},${right},${bottom})\\t(${startTime},${endTime},\\clip(${left},${top},${right},${bottom}))`;
        } else {
          // 右擦除出场：从左边开始逐渐隐藏
          clipTag = `\\clip(${left},${top},${right},${bottom})\\t(${startTime},${endTime},\\clip(${
            right - 1
          },${top},${right},${bottom}))`;
        }
        break;

      case "up":
        if (direction === "in") {
          // 上擦除入场：从上边开始逐渐显示
          clipTag = `\\clip(${left},${top},${right},${
            top + 1
          })\\t(${startTime},${endTime},\\clip(${left},${top},${right},${bottom}))`;
        } else {
          // 上擦除出场：从下边开始逐渐隐藏
          clipTag = `\\clip(${left},${top},${right},${bottom})\\t(${startTime},${endTime},\\clip(${left},${top},${right},${
            top + 1
          }))`;
        }
        break;

      case "down":
        if (direction === "in") {
          // 下擦除入场：从下边开始逐渐显示
          clipTag = `\\clip(${left},${
            bottom - 1
          },${right},${bottom})\\t(${startTime},${endTime},\\clip(${left},${top},${right},${bottom}))`;
        } else {
          // 下擦除出场：从上边开始逐渐隐藏
          clipTag = `\\clip(${left},${top},${right},${bottom})\\t(${startTime},${endTime},\\clip(${left},${
            bottom - 1
          },${right},${bottom}))`;
        }
        break;

      default:
        // 默认左擦除
        clipTag = `\\clip(${left},${top},${
          left + 1
        },${bottom})\\t(${startTime},${endTime},\\clip(${left},${top},${right},${bottom}))`;
    }

    return clipTag;
  }

  /**
   * 生成缩放动画标签
   * 使用fscx/fscy标签实现缩放效果，对应xfade的circleopen
   */
  private static generateZoomAnimation(
    animation: any,
    layoutInfo: TextLayoutInfo,
    timeInfo: { startTime: string; endTime: string },
    direction: "in" | "out"
  ): string {
    const duration = animation.duration || 1000;

    // 计算缩放参数
    const startScale = direction === "in" ? 0 : 100;
    const endScale = direction === "in" ? 100 : 0;

    const startTime = this.parseTimeToMs(timeInfo.startTime);
    const endTime =
      direction === "in"
        ? startTime + duration
        : this.parseTimeToMs(timeInfo.endTime);
    const animStartTime = direction === "in" ? startTime : endTime - duration;
    const animEndTime = direction === "in" ? startTime + duration : endTime;

    // 使用ASS的transform标签实现缩放动画
    return `\\t(${animStartTime},${animEndTime},\\fscx${endScale}\\fscy${endScale})`;
  }

  /**
   * 计算擦除动画的裁剪路径
   */
  private static calculateWipeClipPath(
    layoutInfo: TextLayoutInfo,
    wipeDirection: string,
    direction: "in" | "out"
  ): { clipPath: string } {
    // 简化实现：返回基本的裁剪路径
    // 实际的ASS clip功能比较有限，这里提供基础支持
    const x1 = layoutInfo.textX - 50;
    const y1 = layoutInfo.textY - 25;
    const x2 = layoutInfo.textX + 50;
    const y2 = layoutInfo.textY + 25;

    return {
      clipPath: `m ${x1} ${y1} l ${x2} ${y1} l ${x2} ${y2} l ${x1} ${y2}`,
    };
  }

  /**
   * 计算擦除动画的位置
   * 确保wipe动画从视频外部开始切入
   */
  private static calculateWipePositions(
    layoutInfo: TextLayoutInfo,
    wipeDirection: string,
    direction: "in" | "out"
  ): { startX: number; startY: number; endX: number; endY: number } {
    const normalX = layoutInfo.textX;
    const normalY = layoutInfo.textY;

    // 获取画布尺寸
    const canvasWidth = 1920;
    const canvasHeight = 1080;

    let startX = normalX;
    let startY = normalY;
    let endX = normalX;
    let endY = normalY;

    if (direction === "in") {
      // 入场擦除：从视频外部擦除到正常位置
      endX = normalX;
      endY = normalY;

      switch (wipeDirection) {
        case "left":
          // 从左侧视频外部开始擦除
          startX = -300; // 比slide更远，确保擦除效果明显
          break;
        case "right":
          // 从右侧视频外部开始擦除
          startX = canvasWidth + 300;
          break;
        case "up":
          // 从上方视频外部开始擦除
          startY = -200;
          break;
        case "down":
          // 从下方视频外部开始擦除
          startY = canvasHeight + 200;
          break;
      }
    } else {
      // 出场擦除：从正常位置擦除到视频外部
      startX = normalX;
      startY = normalY;

      switch (wipeDirection) {
        case "left":
          // 擦除到左侧视频外部
          endX = -300;
          break;
        case "right":
          // 擦除到右侧视频外部
          endX = canvasWidth + 300;
          break;
        case "up":
          // 擦除到上方视频外部
          endY = -200;
          break;
        case "down":
          // 擦除到下方视频外部
          endY = canvasHeight + 200;
          break;
      }
    }

    return { startX, startY, endX, endY };
  }

  /**
   * 解析时间字符串为毫秒
   */
  private static parseTimeToMs(timeStr: string): number {
    const parts = timeStr.split(":");
    const hours = parseInt(parts[0]) || 0;
    const minutes = parseInt(parts[1]) || 0;
    const seconds = parseInt(parts[2]) || 0;

    return (hours * 3600 + minutes * 60 + seconds) * 1000;
  }
}
